<template>
  <div class="qr-decoder-view">
    <div class="tool-header">
      <div class="tool-title">
        <QrcodeOutlined class="tool-icon" />
        <div class="tool-info">
          <h1>二维码解码工具</h1>
          <p>支持拖拽、点击选择和剪贴板粘贴二维码图片</p>
        </div>
      </div>

      <div class="tool-actions">
        <Button @click="showHistory" :disabled="loading">
          <HistoryOutlined />
          历史记录
        </Button>
        <Button @click="clearAll" :disabled="loading">
          <DeleteOutlined />
          清空
        </Button>
        <Button @click="showHelp">
          <QuestionCircleOutlined />
          帮助
        </Button>
      </div>
    </div>
    
    <div class="tool-content">
      <!-- 上传区域 -->
      <div class="upload-section">
        <Card class="upload-card">
          <div
            class="upload-area"
            :class="{
              'dragover': isDragOver,
              'has-image': previewImage,
              'loading': loading
            }"
            @click="selectFile"
            @dragover="handleDragOver"
            @dragenter="handleDragEnter"
            @dragleave="handleDragLeave"
            @drop="handleDrop"
          >
            <!-- 加载状态 -->
            <div v-if="loading" class="loading-state">
              <LoadingOutlined :spin="true" class="loading-icon" />
              <h3>正在处理图片...</h3>
              <p>请稍候，正在解码二维码</p>
            </div>
            
            <!-- 图片预览 -->
            <div v-else-if="previewImage" class="preview-state">
              <img
                :src="previewImage"
                alt="二维码预览"
                class="preview-image"
                @load="console.log('图片加载成功')"
                @error="console.log('图片加载失败', $event)"
              />
              <div class="preview-info">
                <p class="file-name">{{ currentFileName }}</p>
                <p class="file-size">{{ currentFileSize }}</p>
                <p class="debug-info" style="font-size: 12px; color: #666;">
                  预览状态: {{ previewImage ? '有数据' : '无数据' }}
                  ({{ previewImage.length }} 字符)
                </p>
              </div>
              <div class="upload-buttons">
                <Button type="primary" @click.stop="selectFile">
                  <FolderOpenOutlined />
                  重新选择
                </Button>
                <Button @click.stop="clearImage">
                  <DeleteOutlined />
                  清除图片
                </Button>
              </div>
            </div>
            
            <!-- 默认上传状态 -->
            <div v-else class="upload-state">
              <CloudUploadOutlined class="upload-icon" />
              <h3>上传二维码图片</h3>
              <p>拖拽图片到此处，或点击选择文件<br>也可以使用 Ctrl+V 粘贴剪贴板中的图片</p>
              <div class="upload-buttons">
                <Button type="primary">
                  <FolderOpenOutlined />
                  选择文件
                </Button>
                <Button @click.stop="pasteFromClipboard">
                  <CopyOutlined />
                  从剪贴板粘贴
                </Button>
                <Button @click.stop="testClipboard" type="dashed">
                  <SearchOutlined />
                  测试Web剪贴板
                </Button>
                <Button @click.stop="testTauriClipboard" type="dashed">
                  <SearchOutlined />
                  测试Tauri剪贴板
                </Button>
                <Button @click.stop="simpleClipboardTest" type="dashed">
                  <BugOutlined />
                  简单剪贴板测试
                </Button>
                <Button @click.stop="detailedClipboardDiagnosis" type="dashed">
                  <BugOutlined />
                  详细剪贴板诊断
                </Button>
                <Button @click.stop="testTauriDragDrop" type="dashed">
                  <CloudUploadOutlined />
                  测试Tauri拖拽
                </Button>
              </div>
            </div>
          </div>
        </Card>
      </div>
      
      <!-- 结果区域 -->
      <div class="result-section">
        <Card class="result-card">
          <template #title>
            <div class="result-title">
              <FileTextOutlined />
              解码结果
              <Tag v-if="decodeStatus" :color="getStatusColor(decodeStatus)" class="status-tag">
                {{ getStatusText(decodeStatus) }}
              </Tag>
            </div>
          </template>
          
          <template #extra>
            <Space>
              <Button
                type="dashed"
                @click="copyResult"
                :disabled="!result"
                class="copy-button"
              >
                <!-- <CopyOutlined v-if="!copyLoading" />使用这个会影响布局，去掉 -->
                复制结果
              </Button>
              <Button
                @click="saveResult"
                :disabled="!result || loading"
                class="save-button"
              >
                <DownloadOutlined />
                保存文件
              </Button>
            </Space>
          </template>

          <div v-if="!result && !loading" class="empty-result">
            <SearchOutlined class="empty-icon" />
            <p>请上传二维码图片进行解码</p>
          </div>
          
          <div v-else-if="result" class="result-content">
            <TextArea
              v-model:value="result"
              :rows="8"
              readonly
              placeholder="解码结果将显示在这里..."
              class="result-textarea"
            />
            <div class="result-meta">
              <Text type="secondary">
                字符数：{{ result.length }} | 
                解码时间：{{ decodeTime }}ms
              </Text>
            </div>
          </div>
        </Card>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInput"
      type="file"
      accept="image/*"
      style="display: none"
      @change="handleFileSelect"
    />

    <!-- 历史记录弹窗 -->
    <Modal
      v-model:open="historyVisible"
      title="解码历史记录"
      width="800px"
      :footer="null"
    >
      <div class="history-content">
        <div v-if="historyList.length === 0" class="empty-history">
          <SearchOutlined class="empty-icon" />
          <p>暂无历史记录</p>
        </div>
        <div v-else class="history-list">
          <div
            v-for="(item, index) in historyList"
            :key="index"
            class="history-item"
          >
            <div class="history-info">
              <div class="history-content-text">{{ item.content }}</div>
              <div class="history-meta">
                <Text type="secondary">
                  {{ formatDate(item.timestamp) }} |
                  字符数：{{ item.content.length }}
                </Text>
              </div>
            </div>
            <div class="history-actions">
              <Button size="small" @click="copyHistoryItem(item.content)">
                <CopyOutlined />
                复制
              </Button>
              <Button size="small" @click="useHistoryItem(item.content)">
                <ImportOutlined />
                使用
              </Button>
              <Button size="small" danger @click="deleteHistoryItem(index)">
                <DeleteOutlined />
                删除
              </Button>
            </div>
          </div>
        </div>
        <div class="history-footer">
          <Button @click="clearHistory" danger>
            <DeleteOutlined />
            清空历史记录
          </Button>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { Card, Button, Input, message, Tag, Space, Typography, Modal } from 'ant-design-vue'
import {
  QrcodeOutlined,
  CloudUploadOutlined,
  CopyOutlined,
  DeleteOutlined,
  QuestionCircleOutlined,
  FolderOpenOutlined,
  SearchOutlined,
  LoadingOutlined,
  FileTextOutlined,
  DownloadOutlined,
  HistoryOutlined,
  ImportOutlined,
  BugOutlined
} from '@ant-design/icons-vue'
import { 
  decodeQRCodeFromFile, 
  decodeQRCodeFromClipboard, 
  validateImageFile,
  formatFileSize,
  generateFileName,
  type QRDecodeResult 
} from '@/utils/qrcode'
import { writeClipboardText, saveToolState, loadToolState } from '@/utils/tauri'

const { TextArea, Text } = Input

// 历史记录接口
interface HistoryItem {
  content: string
  timestamp: number
}

// 响应式数据
const result = ref('')
const loading = ref(false)
const copyLoading = ref(false)
const isDragOver = ref(false)
const previewImage = ref('')
const currentFileName = ref('')
const currentFileSize = ref('')
const decodeStatus = ref<'success' | 'error' | 'processing' | ''>('')
const decodeTime = ref(0)
const isTauriEnv = ref(false)
const fileInput = ref<HTMLInputElement>()

// 历史记录相关
const historyVisible = ref(false)
const historyList = ref<HistoryItem[]>([])

// 状态管理函数
const getStatusColor = (status: string) => {
  switch (status) {
    case 'success': return 'green'
    case 'error': return 'red'
    case 'processing': return 'orange'
    default: return 'default'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'success': return '解码成功'
    case 'error': return '解码失败'
    case 'processing': return '解码中'
    default: return '就绪'
  }
}

// 文件处理函数
const selectFile = () => {
  if (loading.value) return
  fileInput.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    processFile(file)
  }
}

const processFile = async (file: File) => {
  // 验证文件
  const validation = validateImageFile(file)
  if (!validation.valid) {
    message.error(validation.error)
    return
  }

  loading.value = true
  decodeStatus.value = 'processing'
  
  try {
    // 显示文件信息
    currentFileName.value = file.name
    currentFileSize.value = formatFileSize(file.size)
    
    // 创建预览
    const reader = new FileReader()
    reader.onload = (e) => {
      previewImage.value = e.target?.result as string
    }
    reader.readAsDataURL(file)

    // 解码二维码
    const startTime = Date.now()
    const decodeResult = await decodeQRCodeFromFile(file)
    decodeTime.value = Date.now() - startTime

    handleDecodeResult(decodeResult)
  } catch (error) {
    console.error('处理文件失败:', error)
    message.error('处理文件失败')
    decodeStatus.value = 'error'
  } finally {
    loading.value = false
  }
}

const handleDecodeResult = (decodeResult: QRDecodeResult, imageDataUrl?: string) => {
  if (decodeResult.success && decodeResult.data) {
    result.value = decodeResult.data
    decodeStatus.value = 'success'
    message.success('二维码解码成功')

    // 如果提供了图片数据，设置预览
    if (imageDataUrl) {
      previewImage.value = imageDataUrl
      currentFileName.value = 'clipboard-image.png'
      currentFileSize.value = `${Math.round(imageDataUrl.length / 1024)} KB`
    }

    // 添加到历史记录
    addToHistory(decodeResult.data)
  } else {
    result.value = ''
    decodeStatus.value = 'error'
    message.error(decodeResult.error || '解码失败')
  }
}

// HTML 拖拽处理函数（仅在非 Tauri 环境中使用）
const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  event.stopPropagation()
  isDragOver.value = true
}

const handleDragEnter = (event: DragEvent) => {
  event.preventDefault()
  event.stopPropagation()
  isDragOver.value = true
}

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault()
  event.stopPropagation()

  // 只有当鼠标真正离开拖拽区域时才设置为false
  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
  const x = event.clientX
  const y = event.clientY

  if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
    isDragOver.value = false
  }
}

const handleDrop = async (event: DragEvent) => {
  event.preventDefault()
  event.stopPropagation()
  isDragOver.value = false

  console.log('HTML 拖拽文件事件触发')

  const files = event.dataTransfer?.files
  console.log('拖拽文件数量:', files?.length)

  if (files && files.length > 0) {
    const file = files[0]
    console.log('拖拽文件信息:', {
      name: file.name,
      type: file.type,
      size: file.size
    })

    // 在 Tauri 环境中，检查是否是文件路径
    if (isTauriEnv.value && file.name && file.size === 0) {
      // 这可能是一个文件路径而不是实际的文件对象
      console.log('检测到可能的文件路径，尝试使用 Tauri 处理')
      try {
        // 尝试从文件路径读取文件
        const filePath = file.name
        await handleTauriFileDrop(filePath)
        return
      } catch (error) {
        console.warn('Tauri 文件路径处理失败，回退到普通文件处理:', error)
      }
    }

    // 普通文件处理
    processFile(file)
  } else {
    console.log('没有检测到拖拽文件')
    message.warning('请拖拽图片文件到此区域')
  }
}

// 剪贴板处理函数
const pasteFromClipboard = async () => {
  if (loading.value) return

  console.log('开始从剪贴板粘贴...')
  loading.value = true
  decodeStatus.value = 'processing'

  try {
    // 检查是否在 Tauri 环境中
    const { isTauriEnvironment } = await import('@/utils/tauri')

    if (isTauriEnvironment()) {
      console.log('检测到 Tauri 环境，使用原生剪贴板 API')

      // 强制使用 Tauri 剪贴板 API
      const { readClipboardImage, readClipboardText } = await import('@/utils/tauri')

      try {
        // 首先尝试读取图片
        console.log('尝试读取剪贴板图片...')
        const imageData = await readClipboardImage()
        console.log('成功读取剪贴板图片，数据长度:', imageData.length)

        // 设置预览图片
        console.log('设置预览图片，数据格式检查:')
        console.log('- 数据类型:', typeof imageData)
        console.log('- 数据长度:', imageData?.length || 0)
        console.log('- 数据开头:', imageData ? imageData.substring(0, Math.min(50, imageData.length)) : 'null')
        console.log('- 是否包含 data: 前缀:', imageData ? imageData.startsWith('data:') : false)

        previewImage.value = imageData
        currentFileName.value = 'clipboard-image.png'
        currentFileSize.value = `${Math.round(imageData.length / 1024)} KB`

        console.log('预览图片已设置:', previewImage.value.length > 0)

        // 解码二维码
        const { decodeQRCodeFromDataURL } = await import('@/utils/qrcode')
        const startTime = Date.now()
        const decodeResult = await decodeQRCodeFromDataURL(imageData)
        decodeTime.value = Date.now() - startTime

        handleDecodeResult(decodeResult, imageData)
        return
      } catch (imageError) {
        console.log('读取剪贴板图片失败，尝试读取文本:', imageError)

        try {
          // 尝试读取文本（可能是 base64 图片）
          const text = await readClipboardText()
          console.log('成功读取剪贴板文本，长度:', text.length)

          if (text.startsWith('data:image/')) {
            console.log('发现 base64 图片数据')

            // 解码二维码
            const { decodeQRCodeFromDataURL } = await import('@/utils/qrcode')
            const startTime = Date.now()
            const decodeResult = await decodeQRCodeFromDataURL(text)
            decodeTime.value = Date.now() - startTime

            handleDecodeResult(decodeResult, text)
            return
          } else {
            throw new Error('剪贴板中没有找到图片数据')
          }
        } catch (textError) {
          console.error('读取剪贴板文本也失败:', textError)
          const errorMsg = imageError instanceof Error ? imageError.message : '未知错误'
          throw new Error(`剪贴板读取失败: ${errorMsg}`)
        }
      }
    } else {
      console.log('检测到 Web 环境，使用浏览器剪贴板 API')

      // Web 环境下使用原有逻辑
      const startTime = Date.now()
      const decodeResult = await decodeQRCodeFromClipboard()
      decodeTime.value = Date.now() - startTime

      if (!decodeResult.success) {
        message.error({
          content: `剪贴板解码失败: ${decodeResult.error}`,
          duration: 5
        })
        decodeStatus.value = 'error'
      } else {
        handleDecodeResult(decodeResult, decodeResult.imageDataUrl)
      }
    }
  } catch (error) {
    console.error('从剪贴板解码失败:', error)
    const errorMsg = error instanceof Error ? error.message : '从剪贴板解码失败'
    message.error({
      content: errorMsg,
      duration: 5
    })
    decodeStatus.value = 'error'
  } finally {
    loading.value = false
  }
}

// 测试剪贴板功能
const testClipboard = async () => {
  try {
    console.log('=== 剪贴板测试开始 ===')

    // 检查基本API支持
    console.log('navigator.clipboard 支持:', !!navigator.clipboard)
    console.log('window.isSecureContext:', window.isSecureContext)
    console.log('当前协议:', window.location.protocol)

    if (!navigator.clipboard) {
      message.error('当前浏览器不支持剪贴板API')
      return
    }

    if (!window.isSecureContext) {
      message.warning('剪贴板API需要在安全上下文（HTTPS或localhost）中使用')
    }

    // 检查权限
    try {
      const permission = await navigator.permissions.query({ name: 'clipboard-read' as PermissionName })
      console.log('剪贴板读取权限:', permission.state)
    } catch (permError) {
      console.log('权限查询失败:', permError)
    }

    // 尝试读取剪贴板内容
    try {
      const clipboardItems = await navigator.clipboard.read()
      console.log('剪贴板项目数量:', clipboardItems.length)

      for (let i = 0; i < clipboardItems.length; i++) {
        const item = clipboardItems[i]
        console.log(`项目 ${i + 1} 类型:`, item.types)

        for (const type of item.types) {
          try {
            const blob = await item.getType(type)
            console.log(`类型 ${type} 大小:`, blob.size, 'bytes')

            if (type.startsWith('image/')) {
              console.log('发现图片类型:', type)
              message.success(`发现图片: ${type}, 大小: ${blob.size} bytes`)
            }
          } catch (typeError) {
            console.log(`获取类型 ${type} 失败:`, typeError)
          }
        }
      }

      if (clipboardItems.length === 0) {
        message.info('剪贴板为空')
      }
    } catch (readError) {
      console.error('读取剪贴板失败:', readError)
      const errorMsg = readError instanceof Error ? readError.message : '未知错误'
      message.error(`读取剪贴板失败: ${errorMsg}`)
    }

    // 尝试读取文本
    try {
      const text = await navigator.clipboard.readText()
      console.log('剪贴板文本长度:', text.length)
      if (text.length > 0) {
        console.log('文本前100字符:', text.substring(0, 100))
        if (text.startsWith('data:image/') || text.startsWith('iVBORw0KGgo')) {
          message.info('发现可能的base64图片数据')
        }
      }
    } catch (textError) {
      console.log('读取剪贴板文本失败:', textError)
    }

    console.log('=== 剪贴板测试结束 ===')
    message.success('剪贴板测试完成，请查看控制台输出')
  } catch (error) {
    console.error('剪贴板测试失败:', error)
    const errorMsg = error instanceof Error ? error.message : '未知错误'
    message.error(`剪贴板测试失败: ${errorMsg}`)
  }
}

// 测试 Tauri 剪贴板功能
const testTauriClipboard = async () => {
  try {
    console.log('=== Tauri 剪贴板测试开始 ===')

    // 强制导入 Tauri 函数
    const { readClipboardImage, readClipboardText, isTauriEnvironment } = await import('@/utils/tauri')

    console.log('Tauri 环境检测:', isTauriEnvironment())
    console.log('window.__TAURI__:', !!(window as any).__TAURI__)
    console.log('window.__TAURI_INTERNALS__:', !!(window as any).__TAURI_INTERNALS__)

    if (!isTauriEnvironment()) {
      message.error('当前不在 Tauri 环境中')
      return
    }

    // 测试读取剪贴板图片
    try {
      console.log('尝试读取剪贴板图片...')
      const imageData = await readClipboardImage()
      console.log('成功读取剪贴板图片，数据长度:', imageData.length)
      message.success(`成功读取剪贴板图片，数据长度: ${imageData.length}`)

      // 尝试解码
      const { decodeQRCodeFromDataURL } = await import('@/utils/qrcode')
      const decodeResult = await decodeQRCodeFromDataURL(imageData)

      if (decodeResult.success) {
        message.success(`解码成功: ${decodeResult.data}`)
        result.value = decodeResult.data || ''
        decodeStatus.value = 'success'

        // 设置预览图片
        previewImage.value = imageData
        currentFileName.value = 'clipboard-image.png'
        currentFileSize.value = `${Math.round(imageData.length / 1024)} KB`
      } else {
        message.error(`解码失败: ${decodeResult.error}`)
      }
    } catch (imageError) {
      console.log('读取剪贴板图片失败:', imageError)

      // 尝试读取文本
      try {
        console.log('尝试读取剪贴板文本...')
        const text = await readClipboardText()
        console.log('成功读取剪贴板文本，长度:', text.length)
        console.log('文本前100字符:', text.substring(0, 100))

        if (text.startsWith('data:image/')) {
          message.info('发现 base64 图片数据，尝试解码...')
          const { decodeQRCodeFromDataURL } = await import('@/utils/qrcode')
          const decodeResult = await decodeQRCodeFromDataURL(text)

          if (decodeResult.success) {
            message.success(`解码成功: ${decodeResult.data}`)
            result.value = decodeResult.data || ''
            decodeStatus.value = 'success'

            // 设置预览图片
            previewImage.value = text
            currentFileName.value = 'clipboard-image.png'
            currentFileSize.value = `${Math.round(text.length / 1024)} KB`
          } else {
            message.error(`解码失败: ${decodeResult.error}`)
          }
        } else {
          message.warning('剪贴板中的文本不是图片数据')
        }
      } catch (textError) {
        console.error('读取剪贴板文本也失败:', textError)
        const errorMsg = imageError instanceof Error ? imageError.message : '未知错误'
        message.error(`剪贴板读取完全失败: ${errorMsg}`)
      }
    }

    console.log('=== Tauri 剪贴板测试结束 ===')
  } catch (error) {
    console.error('Tauri 剪贴板测试失败:', error)
    const errorMsg = error instanceof Error ? error.message : '未知错误'
    message.error(`Tauri 剪贴板测试失败: ${errorMsg}`)
  }
}

// 简单剪贴板测试
const simpleClipboardTest = async () => {
  try {
    console.log('=== 简单剪贴板测试开始 ===')

    // 检查环境
    const { isTauriEnvironment } = await import('@/utils/tauri')
    const isInTauri = isTauriEnvironment()

    console.log('当前环境:', isInTauri ? 'Tauri' : 'Web')
    console.log('window.location.href:', window.location.href)
    console.log('window.isSecureContext:', window.isSecureContext)

    if (isInTauri) {
      // Tauri 环境测试
      console.log('测试 Tauri 剪贴板...')
      const { readClipboardText } = await import('@/utils/tauri')

      try {
        const text = await readClipboardText()
        console.log('Tauri 剪贴板文本读取成功，长度:', text.length)
        console.log('文本内容（前50字符）:', text.substring(0, 50))
        message.success(`Tauri 剪贴板读取成功，文本长度: ${text.length}`)
      } catch (error) {
        console.error('Tauri 剪贴板文本读取失败:', error)
        message.error(`Tauri 剪贴板读取失败: ${error}`)
      }
    } else {
      // Web 环境测试
      console.log('测试 Web 剪贴板...')

      if (!navigator.clipboard) {
        message.error('浏览器不支持剪贴板 API')
        return
      }

      try {
        const text = await navigator.clipboard.readText()
        console.log('Web 剪贴板文本读取成功，长度:', text.length)
        console.log('文本内容（前50字符）:', text.substring(0, 50))
        message.success(`Web 剪贴板读取成功，文本长度: ${text.length}`)
      } catch (error) {
        console.error('Web 剪贴板文本读取失败:', error)
        message.error(`Web 剪贴板读取失败: ${error}`)
      }
    }

    console.log('=== 简单剪贴板测试结束 ===')
  } catch (error) {
    console.error('简单剪贴板测试失败:', error)
    message.error(`测试失败: ${error}`)
  }
}

// 详细剪贴板诊断
const detailedClipboardDiagnosis = async () => {
  try {
    console.log('=== 详细剪贴板诊断开始 ===')

    const { isTauriEnvironment } = await import('@/utils/tauri')
    const isInTauri = isTauriEnvironment()

    console.log('环境信息:')
    console.log('- 当前环境:', isInTauri ? 'Tauri' : 'Web')
    console.log('- User Agent:', navigator.userAgent)
    console.log('- 平台:', navigator.platform)

    if (isInTauri) {
      console.log('=== Tauri 剪贴板诊断 ===')

      // 尝试直接调用 Tauri 命令
      try {
        const { invoke } = await import('@tauri-apps/api/core')

        // 测试文本读取
        console.log('测试 Tauri 文本读取...')
        try {
          const text = await invoke('read_clipboard_text') as string
          console.log('✅ 文本读取成功，长度:', text.length)
          console.log('文本内容（前100字符）:', text.substring(0, 100))
        } catch (textError) {
          console.log('❌ 文本读取失败:', textError)
        }

        // 测试图片读取
        console.log('测试 Tauri 图片读取...')
        try {
          const imageData = await invoke('read_clipboard_image') as string
          console.log('✅ 图片读取成功，数据长度:', imageData.length)
          console.log('数据类型:', typeof imageData)
          console.log('数据开头:', imageData.substring(0, 50))
          message.success(`图片读取成功！数据长度: ${imageData.length}`)
        } catch (imageError) {
          console.log('❌ 图片读取失败:', imageError)
          message.warning(`图片读取失败: ${imageError}`)
        }

      } catch (invokeError) {
        console.error('❌ Tauri invoke 调用失败:', invokeError)
        message.error(`Tauri 调用失败: ${invokeError}`)
      }
    } else {
      console.log('=== Web 剪贴板诊断 ===')

      if (!navigator.clipboard) {
        console.log('❌ 浏览器不支持 Clipboard API')
        message.error('浏览器不支持 Clipboard API')
        return
      }

      console.log('✅ 浏览器支持 Clipboard API')
      console.log('- isSecureContext:', window.isSecureContext)
      console.log('- protocol:', window.location.protocol)

      // 测试权限
      try {
        const permission = await navigator.permissions.query({ name: 'clipboard-read' as PermissionName })
        console.log('- 剪贴板读取权限:', permission.state)
      } catch (permError) {
        console.log('- 权限查询失败:', permError)
      }

      // 测试读取
      try {
        const items = await navigator.clipboard.read()
        console.log('✅ 剪贴板读取成功，项目数量:', items.length)

        for (let i = 0; i < items.length; i++) {
          const item = items[i]
          console.log(`项目 ${i + 1}:`)
          console.log('- 类型:', item.types)

          for (const type of item.types) {
            try {
              const blob = await item.getType(type)
              console.log(`  - ${type}: ${blob.size} bytes`)

              if (type.startsWith('image/')) {
                message.success(`发现图片: ${type}, 大小: ${blob.size} bytes`)
              }
            } catch (typeError) {
              console.log(`  - ${type}: 读取失败 -`, typeError)
            }
          }
        }
      } catch (readError) {
        console.log('❌ 剪贴板读取失败:', readError)
        message.error(`剪贴板读取失败: ${readError}`)
      }
    }

    console.log('=== 详细剪贴板诊断结束 ===')
    message.info('诊断完成，请查看控制台输出')
  } catch (error) {
    console.error('诊断失败:', error)
    message.error(`诊断失败: ${error}`)
  }
}

// 复制结果
const copyResult = async () => {
  if (!result.value || copyLoading.value) return

  copyLoading.value = true
  try {
    await writeClipboardText(result.value)
    message.success('复制成功')
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败')
  } finally {
    copyLoading.value = false
  }
}

// 保存结果
const saveResult = () => {
  if (!result.value) return

  try {
    const fileName = generateFileName('qr-decode-result', 'txt')
    const blob = new Blob([result.value], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = fileName
    a.click()
    URL.revokeObjectURL(url)
    message.success('文件保存成功')
  } catch (error) {
    console.error('保存文件失败:', error)
    message.error('保存文件失败')
  }
}

// 清除图片
const clearImage = () => {
  previewImage.value = ''
  currentFileName.value = ''
  currentFileSize.value = ''
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

// 清空所有
const clearAll = () => {
  result.value = ''
  decodeStatus.value = ''
  decodeTime.value = 0
  clearImage()
  message.info('已清空')
}

// 显示帮助
const showHelp = () => {
  message.info({
    content: `二维码解码工具使用说明：

1. 支持的操作方式：
   • 拖拽图片到上传区域
   • 点击"选择文件"按钮选择图片
   • 使用 Ctrl+V 粘贴剪贴板中的图片

2. 支持的图片格式：
   • JPG/JPEG、PNG、GIF、BMP、WebP

3. 功能特性：
   • 自动识别二维码内容
   • 一键复制解码结果
   • 保存结果为文本文件
   • 实时状态提示

4. 快捷键：
   • Ctrl+V：粘贴剪贴板图片`,
    duration: 10
  })
}

// 历史记录管理
const addToHistory = (content: string) => {
  // 避免重复添加相同内容
  if (historyList.value.some(item => item.content === content)) {
    return
  }

  const historyItem: HistoryItem = {
    content,
    timestamp: Date.now()
  }

  // 添加到列表开头，最多保留50条记录
  historyList.value.unshift(historyItem)
  if (historyList.value.length > 50) {
    historyList.value = historyList.value.slice(0, 50)
  }

  // 保存到本地存储
  saveHistory()
}

const loadHistory = async () => {
  try {
    const saved = await loadToolState('qr-decoder-history')
    if (saved) {
      historyList.value = JSON.parse(saved)
    }
  } catch (error) {
    console.error('加载历史记录失败:', error)
  }
}

const saveHistory = async () => {
  try {
    await saveToolState('qr-decoder-history', JSON.stringify(historyList.value))
  } catch (error) {
    console.error('保存历史记录失败:', error)
  }
}

const showHistory = () => {
  historyVisible.value = true
}

const formatDate = (timestamp: number) => {
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const copyHistoryItem = async (content: string) => {
  try {
    await writeClipboardText(content)
    message.success('复制成功')
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败')
  }
}

const useHistoryItem = (content: string) => {
  result.value = content
  decodeStatus.value = 'success'
  historyVisible.value = false
  message.success('已使用历史记录')
}

const deleteHistoryItem = (index: number) => {
  historyList.value.splice(index, 1)
  saveHistory()
  message.success('删除成功')
}

const clearHistory = () => {
  historyList.value = []
  saveHistory()
  message.success('历史记录已清空')
}

// 测试 Tauri 拖拽功能
const testTauriDragDrop = async () => {
  try {
    console.log('=== Tauri 拖拽测试开始 ===')

    // 详细的环境检测
    console.log('window 对象存在:', typeof window !== 'undefined')
    console.log('window.__TAURI__:', !!(window as any).__TAURI__)
    console.log('window.__TAURI_INTERNALS__:', !!(window as any).__TAURI_INTERNALS__)
    console.log('window.location.protocol:', window.location.protocol)
    console.log('window.location.href:', window.location.href)

    const { isTauriEnvironment } = await import('@/utils/tauri')
    console.log('isTauriEnvironment():', isTauriEnvironment())

    // 尝试导入 Tauri API
    try {
      const { listen } = await import('@tauri-apps/api/event')
      console.log('成功导入 @tauri-apps/api/event')

      message.info({
        content: '请拖拽一个图片文件到窗口中进行测试...',
        duration: 5
      })

      // 临时添加一个测试监听器
      const testUnlisten = await listen('tauri://file-drop', (event: any) => {
        console.log('测试监听器收到拖拽事件:', event)
        message.success(`测试成功！收到拖拽文件: ${event.payload?.[0] || '未知'}`)
        testUnlisten() // 清理测试监听器
      })

      // 5秒后自动清理测试监听器
      setTimeout(() => {
        testUnlisten()
        console.log('测试监听器已清理')
      }, 5000)

      console.log('=== Tauri 拖拽测试设置完成 ===')
    } catch (importError) {
      console.error('导入 Tauri API 失败:', importError)
      message.error(`无法导入 Tauri API: ${importError}`)
    }
  } catch (error) {
    console.error('Tauri 拖拽测试失败:', error)
    const errorMsg = error instanceof Error ? error.message : '未知错误'
    message.error(`Tauri 拖拽测试失败: ${errorMsg}`)
  }
}

// 处理 Tauri 文件拖拽
const handleTauriFileDrop = async (filePath: string) => {
  if (loading.value) return

  console.log('处理 Tauri 拖拽文件:', filePath)
  loading.value = true
  decodeStatus.value = 'processing'

  try {
    // 检查文件扩展名
    const ext = filePath.toLowerCase().split('.').pop()
    const supportedExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']

    if (!ext || !supportedExts.includes(ext)) {
      throw new Error(`不支持的文件格式。支持的格式：${supportedExts.join(', ')}`)
    }

    // 使用 Tauri API 读取文件
    const { readFileAsBase64 } = await import('@/utils/tauri')
    const base64Data = await readFileAsBase64(filePath)
    const dataURL = `data:image/${ext === 'jpg' ? 'jpeg' : ext};base64,${base64Data}`

    // 设置预览
    previewImage.value = dataURL
    currentFileName.value = filePath.split(/[/\\]/).pop() || 'unknown'

    // 获取文件信息
    try {
      const { getFileInfo } = await import('@/utils/tauri')
      const fileInfo = await getFileInfo(filePath)
      currentFileSize.value = `${Math.round(fileInfo.size / 1024)} KB`
    } catch (infoError) {
      console.log('获取文件信息失败:', infoError)
      currentFileSize.value = `${Math.round(base64Data.length * 0.75 / 1024)} KB`
    }

    // 解码二维码
    const { decodeQRCodeFromDataURL } = await import('@/utils/qrcode')
    const startTime = Date.now()
    const decodeResult = await decodeQRCodeFromDataURL(dataURL)
    decodeTime.value = Date.now() - startTime

    handleDecodeResult(decodeResult, dataURL)
  } catch (error) {
    console.error('Tauri 文件拖拽处理失败:', error)
    const errorMsg = error instanceof Error ? error.message : '文件处理失败'
    message.error(errorMsg)
    decodeStatus.value = 'error'
  } finally {
    loading.value = false
  }
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.ctrlKey && event.key === 'v') {
    // 检查当前焦点是否在输入框或文本区域上
    const activeElement = document.activeElement
    const isInputElement = activeElement && (
      activeElement.tagName === 'INPUT' ||
      activeElement.tagName === 'TEXTAREA' ||
      (activeElement as HTMLElement).contentEditable === 'true' ||
      activeElement.getAttribute('contenteditable') === 'true'
    )

    // 只有当焦点不在输入元素上时才触发图片粘贴
    if (!isInputElement) {
      console.log('检测到 Ctrl+V，当前焦点不在输入元素上，触发图片粘贴')
      event.preventDefault()
      pasteFromClipboard()
    } else {
      console.log('检测到 Ctrl+V，但当前焦点在输入元素上，允许默认粘贴行为')
      // 不阻止默认行为，让输入框正常处理粘贴
    }
  }
}

// 阻止浏览器默认的文件拖拽行为
const preventBrowserFileDrop = (e: DragEvent) => {
  e.preventDefault()
  e.stopPropagation()
}

// 生命周期
onMounted(async () => {
  document.addEventListener('keydown', handleKeydown)
  loadHistory()

  // 阻止浏览器默认的文件拖拽行为（防止文件被浏览器打开）
  document.addEventListener('dragover', preventBrowserFileDrop)
  document.addEventListener('drop', preventBrowserFileDrop)

  // 检查 Tauri 环境
  try {
    const { isTauriEnvironment } = await import('@/utils/tauri')
    const isInTauri = isTauriEnvironment()
    isTauriEnv.value = isInTauri

    console.log('检查 Tauri 环境:', isInTauri)
    console.log('使用 HTML 拖拽处理（dragDropEnabled: false）')
  } catch (error) {
    console.error('检查 Tauri 环境失败:', error)
    isTauriEnv.value = false
  }
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  document.removeEventListener('dragover', preventBrowserFileDrop)
  document.removeEventListener('drop', preventBrowserFileDrop)
})
</script>

<style lang="less" scoped>
.qr-decoder-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 24px;
  gap: 24px;
  overflow: hidden;
}

.tool-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 16px;
  border-bottom: 1px solid #e2e8f0;
}

.tool-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.tool-icon {
  font-size: 32px;
  color: #3b82f6;
}

.tool-info {
  h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: #1e293b;
  }

  p {
    margin: 4px 0 0 0;
    font-size: 14px;
    color: #64748b;
  }
}

.tool-actions {
  display: flex;
  gap: 12px;
}

.tool-content {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  overflow: hidden;
}

.upload-section, .result-section {
  display: flex;
  flex-direction: column;
}

.upload-card, .result-card {
  height: 100%;

  :deep(.ant-card-body) {
    height: calc(100% - 57px);
    display: flex;
    flex-direction: column;
  }
}

.upload-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px;
  border: 2px dashed #cbd5e1;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;

  &:hover {
    border-color: #3b82f6;
    background: #eff6ff;
  }

  &.dragover {
    border-color: #3b82f6;
    background: #eff6ff;
    transform: scale(1.02);
  }

  &.loading {
    cursor: not-allowed;
    border-color: #fbbf24;
    background: #fef3c7;
  }

  &.has-image {
    padding: 20px;
    justify-content: flex-start;
  }
}

.loading-state, .upload-state, .preview-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.loading-icon, .upload-icon {
  font-size: 48px;
  color: #94a3b8;
  margin-bottom: 16px;
}

.loading-icon {
  color: #fbbf24;
}

.upload-area h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #1e293b;
}

.upload-area p {
  margin: 0 0 24px 0;
  color: #64748b;
  line-height: 1.5;
}

.upload-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;

  :deep(.ant-btn) {
    min-width: 120px;
  }
}

.preview-image {
  max-width: 100%;
  max-height: 200px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.preview-info {
  margin-bottom: 16px;
  text-align: center;

  .file-name {
    font-weight: 500;
    color: #1e293b;
    margin: 0 0 4px 0;
    word-break: break-all;
  }

  .file-size {
    font-size: 12px;
    color: #64748b;
    margin: 0;
  }
}

.result-title {
  display: flex;
  align-items: center;
  gap: 8px;

  .status-tag {
    margin-left: 8px;
  }
}

.empty-result {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #94a3b8;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.result-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.result-textarea {
  flex: 1;
  resize: none;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.result-meta {
  margin-top: 12px;
  text-align: right;
}

.copy-button {
  min-width: 100px !important;

  :deep(.ant-btn-loading-icon) {
    margin-right: 8px;
  }
}

.save-button {
  min-width: 100px !important;
}

// 历史记录样式
.history-content {
  max-height: 500px;
  overflow-y: auto;
}

.empty-history {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #94a3b8;
  text-align: center;

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #f8fafc;
  transition: all 0.2s;

  &:hover {
    border-color: #3b82f6;
    background: #eff6ff;
  }
}

.history-info {
  flex: 1;
  margin-right: 16px;
}

.history-content-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #1e293b;
  margin-bottom: 8px;
  word-break: break-all;
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.history-meta {
  font-size: 12px;
}

.history-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.history-footer {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
  text-align: center;
}

// 响应式设计
@media (max-width: 768px) {
  .tool-content {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .upload-area {
    padding: 20px;
  }

  .upload-buttons {
    flex-direction: column;
    width: 100%;

    :deep(.ant-btn) {
      width: 100%;
    }
  }

  .history-item {
    flex-direction: column;
    gap: 12px;
  }

  .history-info {
    margin-right: 0;
  }

  .history-actions {
    align-self: flex-end;
  }
}
</style>
