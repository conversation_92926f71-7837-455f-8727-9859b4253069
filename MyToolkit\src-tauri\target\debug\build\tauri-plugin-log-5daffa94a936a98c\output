cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=D:\projects\vscode-projects\erweima\MyToolkit\src-tauri\target\debug\build\tauri-plugin-log-5daffa94a936a98c\out\tauri-plugin-log-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_API_SCRIPT_PATH=\\?\D:\rust-repo\.cargo\registry\src\mirrors.aliyun.com-0671735e7cc7f5e7\tauri-plugin-log-2.4.0\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
