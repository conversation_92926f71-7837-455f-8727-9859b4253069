# MyToolkit 第4-5开发交付文档

## 项目概述
MyToolkit 是一个基于 Tauri + Vue 3 + TypeScript 的跨平台桌面工具箱应用，提供二维码生成/解码、JSON格式化等实用工具。

## 本周期主要成果

### 1. 应用架构完善
- ✅ 完成 Tauri v2 项目初始化和配置
- ✅ 建立 Vue 3 + TypeScript + Ant Design Vue 前端架构
- ✅ 实现响应式布局和组件化设计
- ✅ 配置开发环境和构建流程

### 2. 核心功能实现
- ✅ 二维码解码工具（支持多种图片格式）
- ✅ 二维码生成工具（支持多种配置选项）
- ✅ JSON 格式化工具（语法高亮、错误提示）
- ✅ 文件拖拽上传功能
- ✅ 剪贴板图片粘贴功能
- ✅ 历史记录管理

### 3. 跨平台兼容性
- ✅ Web 端完整功能支持
- ✅ 桌面端 Tauri 应用支持
- ✅ 统一的用户体验

## 重要技术问题及解决方案

### 问题1: Tauri 文件拖拽事件冲突

**问题描述：**
在 Tauri v2 中，原生文件拖拽事件与 HTML 拖拽事件存在冲突，导致：
- 桌面应用中拖拽文件无响应
- Web 端拖拽文件被浏览器直接打开
- 事件监听器无法正常触发

**根本原因：**
1. Tauri 的 `dragDropEnabled: true`（默认）会拦截系统拖拽事件
2. HTML 拖拽事件与 Tauri 拖拽事件系统冲突
3. 浏览器默认行为会直接打开拖拽的文件

**解决方案：**
```json
// tauri.conf.json
{
  "app": {
    "windows": [
      {
        "dragDropEnabled": false  // 关键配置
      }
    ]
  }
}
```

```javascript
// 前端代码
onMounted(() => {
  // 阻止浏览器默认的文件拖拽行为
  document.addEventListener('dragover', preventBrowserFileDrop)
  document.addEventListener('drop', preventBrowserFileDrop)
  
  // 使用统一的 HTML 拖拽事件处理
  // 在 Web 和桌面环境中都能正常工作
})

const preventBrowserFileDrop = (e: DragEvent) => {
  e.preventDefault()
  e.stopPropagation()
}
```

**效果：**
- ✅ 桌面应用拖拽功能正常
- ✅ Web 端拖拽功能正常
- ✅ 统一的事件处理逻辑
- ✅ 防止文件被意外打开

### 问题3: 剪贴板粘贴功能冲突

**问题描述：**
在修复拖拽功能后，出现了新的剪贴板粘贴冲突问题：
- 全局 Ctrl+V 监听导致搜索框等输入元素无法正常粘贴文本
- 用户在输入框中按 Ctrl+V 时，触发了图片粘贴功能而不是文本粘贴
- 剪贴板读取失败，提示"剪贴板中没有图片"

**根本原因：**
1. 全局键盘事件监听器没有区分当前焦点元素
2. 所有 Ctrl+V 操作都被拦截并尝试进行图片粘贴
3. 缺少对输入元素的特殊处理逻辑

**解决方案：**
```javascript
// 智能键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.ctrlKey && event.key === 'v') {
    // 检查当前焦点是否在输入框或文本区域上
    const activeElement = document.activeElement
    const isInputElement = activeElement && (
      activeElement.tagName === 'INPUT' ||
      activeElement.tagName === 'TEXTAREA' ||
      (activeElement as HTMLElement).contentEditable === 'true' ||
      activeElement.getAttribute('contenteditable') === 'true'
    )

    // 只有当焦点不在输入元素上时才触发图片粘贴
    if (!isInputElement) {
      console.log('检测到 Ctrl+V，当前焦点不在输入元素上，触发图片粘贴')
      event.preventDefault()
      pasteFromClipboard()
    } else {
      console.log('检测到 Ctrl+V，但当前焦点在输入元素上，允许默认粘贴行为')
      // 不阻止默认行为，让输入框正常处理粘贴
    }
  }
}
```

**效果：**
- ✅ 搜索框和其他输入元素可以正常粘贴文本
- ✅ 在非输入区域按 Ctrl+V 可以粘贴图片
- ✅ 智能区分文本粘贴和图片粘贴场景
- ✅ 保持良好的用户体验

### 问题2: Tauri v2 权限配置错误

**问题描述：**
初始配置中使用了 Tauri v1 的权限标识符，导致编译失败。

**解决方案：**
```json
// src-tauri/capabilities/default.json
{
  "permissions": [
    "core:default",
    "core:event:default",
    "core:window:default",
    "core:app:default",
    "core:image:default",
    "core:resources:default",
    "core:menu:default",
    "core:path:default",
    "core:webview:default"
  ]
}
```

## 技术栈详情

### 前端技术
- **框架**: Vue 3.3.8 (Composition API)
- **语言**: TypeScript 5.x
- **UI库**: Ant Design Vue 4.0.8
- **图标**: @ant-design/icons-vue 7.0.1
- **构建工具**: Vite 5.0.5
- **二维码处理**: jsqr 1.4.0, qrcode 1.5.3

### 桌面端技术
- **框架**: Tauri v2.5.0
- **语言**: Rust (后端)
- **剪贴板**: arboard crate
- **图像处理**: image crate
- **Base64编码**: base64 crate

### 开发工具
- **代码检查**: ESLint + TypeScript ESLint
- **代码格式化**: Prettier
- **测试框架**: Vitest
- **包管理**: npm

## 项目结构
```
MyToolkit/
├── src/                    # 前端源码
│   ├── components/         # 公共组件
│   ├── views/             # 页面组件
│   ├── utils/             # 工具函数
│   └── assets/            # 静态资源
├── src-tauri/             # Tauri 后端
│   ├── src/               # Rust 源码
│   ├── capabilities/      # 权限配置
│   └── tauri.conf.json    # Tauri 配置
├── docs/                  # 文档
└── dist/                  # 构建输出
```

## 下一阶段计划

### 第7-9周目标
1. **功能扩展**
   - 添加更多实用工具
   - 实现工具状态持久化
   - 添加主题切换功能

2. **性能优化**
   - 代码分割和懒加载
   - 图片处理性能优化
   - 内存使用优化

3. **用户体验**
   - 添加快捷键支持
   - 改进错误处理和用户反馈
   - 添加使用教程和帮助文档

4. **测试和部署**
   - 完善单元测试覆盖
   - 自动化构建和发布流程
   - 多平台兼容性测试

## 已知问题
- [ ] 大文件处理性能有待优化
- [ ] 需要添加更多错误边界处理
- [ ] 某些特殊格式的二维码识别准确率有待提升

## 总结
本周期成功建立了完整的跨平台应用架构，实现了核心功能，并解决了多个关键的技术难题：

### 主要成就
1. **完整的跨平台架构**：建立了 Tauri + Vue 3 + TypeScript 的现代化应用架构
2. **核心功能实现**：二维码生成/解码、JSON格式化、文件拖拽、剪贴板操作等
3. **关键问题解决**：
   - Tauri 文件拖拽事件冲突问题
   - 剪贴板粘贴功能冲突问题
   - Tauri v2 权限配置问题

### 技术突破
- **统一的拖拽处理方案**：通过 `dragDropEnabled: false` 配置，实现了 Web 和桌面环境的统一拖拽体验
- **智能粘贴功能**：通过焦点检测，实现了文本粘贴和图片粘贴的智能区分
- **跨平台兼容性**：确保了所有功能在 Web 和桌面环境中都能正常工作

### 开发经验
通过解决这些复杂的跨平台兼容性问题，团队积累了宝贵的 Tauri 开发经验，为后续功能扩展和性能优化奠定了坚实基础。
