//! Tauri 命令单元测试

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;
    use tempfile::TempDir;
    use tokio;

    #[tokio::test]
    async fn test_read_clipboard_text() {
        // 测试剪贴板文本读取
        let result = read_clipboard_text().await;
        
        // 由于剪贴板内容不可预测，我们只测试函数不会崩溃
        match result {
            Ok(_) => {
                // 成功读取剪贴板
                println!("剪贴板文本读取成功");
            }
            Err(e) => {
                // 在某些环境下可能无法访问剪贴板
                println!("剪贴板访问失败: {}", e);
            }
        }
    }

    #[tokio::test]
    async fn test_read_clipboard_image() {
        // 测试剪贴板图片读取
        let result = read_clipboard_image().await;
        
        // 由于剪贴板内容不可预测，我们只测试函数不会崩溃
        match result {
            Ok(_) => {
                println!("剪贴板图片读取成功");
            }
            Err(e) => {
                println!("剪贴板图片读取失败: {}", e);
            }
        }
    }

    #[tokio::test]
    async fn test_write_clipboard_text() {
        let test_text = "测试文本内容";
        
        let result = write_clipboard_text(test_text.to_string()).await;
        
        match result {
            Ok(_) => {
                println!("剪贴板文本写入成功");
                
                // 尝试读取验证
                if let Ok(read_text) = read_clipboard_text().await {
                    assert_eq!(read_text, test_text);
                }
            }
            Err(e) => {
                println!("剪贴板文本写入失败: {}", e);
            }
        }
    }

    #[tokio::test]
    async fn test_save_file() {
        let temp_dir = TempDir::new().expect("创建临时目录失败");
        let file_path = temp_dir.path().join("test.txt");
        let test_content = "测试文件内容";

        let result = write_file_content(
            file_path.to_string_lossy().to_string(),
            test_content.to_string()
        );

        assert!(result.is_ok(), "文件保存应该成功");

        // 验证文件内容
        let saved_content = fs::read_to_string(&file_path)
            .expect("读取保存的文件失败");
        assert_eq!(saved_content, test_content);
    }

    #[tokio::test]
    async fn test_read_file() {
        let temp_dir = TempDir::new().expect("创建临时目录失败");
        let file_path = temp_dir.path().join("test.txt");
        let test_content = "测试文件内容";

        // 先创建测试文件
        fs::write(&file_path, test_content).expect("创建测试文件失败");

        let result = read_file_content(file_path.to_string_lossy().to_string());

        assert!(result.is_ok(), "文件读取应该成功");
        assert_eq!(result.unwrap(), test_content);
    }

    #[tokio::test]
    async fn test_read_nonexistent_file() {
        let nonexistent_path = "/path/that/does/not/exist.txt";

        let result = read_file_content(nonexistent_path.to_string());

        assert!(result.is_err(), "读取不存在的文件应该失败");
    }

    #[test]
    fn test_get_app_info() {
        let name = get_app_name();
        let version = get_app_version();

        assert!(!name.is_empty(), "应用名称不应为空");
        assert!(!version.is_empty(), "应用版本不应为空");
        assert_eq!(name, "MyToolkit");
    }

    #[test]
    fn test_validate_file_path() {
        // 测试有效路径
        assert!(validate_file_path("./test.txt"));
        assert!(validate_file_path("/home/<USER>/test.txt"));
        assert!(validate_file_path("C:\\Users\\<USER>\\..\\..\\windows\\system32"));
    }

    #[test]
    fn test_sanitize_filename() {
        assert_eq!(sanitize_filename("normal.txt"), "normal.txt");
        assert_eq!(sanitize_filename("file with spaces.txt"), "file with spaces.txt");
        assert_eq!(sanitize_filename("file<>:\"|?*.txt"), "file.txt");
        assert_eq!(sanitize_filename(""), "untitled");
    }

    #[tokio::test]
    async fn test_error_handling() {
        // 测试各种错误情况的处理
        
        // 1. 无效的文件路径
        let result = write_file_content("".to_string(), "content".to_string());
        assert!(result.is_err());

        // 2. 权限不足的路径（在某些系统上）
        let result = write_file_content("/root/test.txt".to_string(), "content".to_string());
        // 这个测试可能在某些环境下通过，所以我们只检查它不会崩溃
        match result {
            Ok(_) => println!("意外地成功写入了受保护的路径"),
            Err(_) => println!("正确地拒绝了受保护的路径"),
        }
    }

    #[tokio::test]
    async fn test_large_file_handling() {
        let temp_dir = TempDir::new().expect("创建临时目录失败");
        let file_path = temp_dir.path().join("large_test.txt");
        
        // 创建一个较大的测试内容（1MB）
        let large_content = "x".repeat(1024 * 1024);
        
        let result = write_file_content(
            file_path.to_string_lossy().to_string(),
            large_content.clone()
        );

        assert!(result.is_ok(), "大文件保存应该成功");

        // 读取并验证
        let result = read_file_content(file_path.to_string_lossy().to_string());
        assert!(result.is_ok(), "大文件读取应该成功");
        assert_eq!(result.unwrap().len(), large_content.len());
    }

    #[tokio::test]
    async fn test_unicode_content() {
        let temp_dir = TempDir::new().expect("创建临时目录失败");
        let file_path = temp_dir.path().join("unicode_test.txt");
        let unicode_content = "测试中文内容 🚀 emoji 和其他 Unicode 字符 ñáéíóú";
        
        let result = write_file_content(
            file_path.to_string_lossy().to_string(),
            unicode_content.to_string()
        );

        assert!(result.is_ok(), "Unicode 内容保存应该成功");

        let result = read_file_content(file_path.to_string_lossy().to_string());
        assert!(result.is_ok(), "Unicode 内容读取应该成功");
        assert_eq!(result.unwrap(), unicode_content);
    }
}

// 辅助函数用于测试
fn validate_file_path(path: &str) -> bool {
    if path.is_empty() {
        return false;
    }
    
    // 检查路径遍历攻击
    if path.contains("..") {
        return false;
    }
    
    true
}

fn sanitize_filename(filename: &str) -> String {
    if filename.is_empty() {
        return "untitled".to_string();
    }
    
    // 移除不安全的字符
    let safe_chars: String = filename
        .chars()
        .filter(|c| !r#"<>:"|?*"#.contains(*c))
        .collect();
    
    if safe_chars.is_empty() {
        "untitled".to_string()
    } else {
        safe_chars
    }
}
