/**
 * 应用性能优化脚本
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 优化配置
const optimizations = {
  // Vite 构建优化
  vite: {
    // 代码分割
    codeSplitting: true,
    // 压缩配置
    minify: 'terser',
    // Tree shaking
    treeShaking: true,
    // 资源内联阈值
    assetsInlineLimit: 4096
  },
  
  // Tauri 优化
  tauri: {
    // 包体积优化
    bundleOptimization: true,
    // 启动优化
    startupOptimization: true,
    // 内存优化
    memoryOptimization: true
  },
  
  // 前端优化
  frontend: {
    // 懒加载
    lazyLoading: true,
    // 图片优化
    imageOptimization: true,
    // 缓存策略
    caching: true
  }
}

/**
 * 优化 Vite 配置
 */
function optimizeViteConfig() {
  const viteConfigPath = path.join(__dirname, '../vite.config.ts')
  
  if (!fs.existsSync(viteConfigPath)) {
    console.error('❌ vite.config.ts 文件不存在')
    return false
  }
  
  let config = fs.readFileSync(viteConfigPath, 'utf8')
  
  // 添加构建优化配置
  const buildOptimizations = `
  build: {
    // 代码分割
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          antd: ['ant-design-vue'],
          utils: ['jsqr', 'qrcode']
        }
      }
    },
    // 压缩配置
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    },
    // 资源内联阈值
    assetsInlineLimit: 4096,
    // 启用 gzip
    reportCompressedSize: true
  },`
  
  // 检查是否已经有构建配置
  if (!config.includes('build:')) {
    // 在 defineConfig 中添加构建配置
    config = config.replace(
      /export default defineConfig\(\{/,
      `export default defineConfig({${buildOptimizations}`
    )
    
    fs.writeFileSync(viteConfigPath, config)
    console.log('✅ Vite 构建配置已优化')
  } else {
    console.log('ℹ️  Vite 构建配置已存在')
  }
  
  return true
}

/**
 * 优化 Tauri 配置
 */
function optimizeTauriConfig() {
  const tauriConfigPath = path.join(__dirname, '../src-tauri/tauri.conf.json')
  
  if (!fs.existsSync(tauriConfigPath)) {
    console.error('❌ tauri.conf.json 文件不存在')
    return false
  }
  
  const config = JSON.parse(fs.readFileSync(tauriConfigPath, 'utf8'))
  
  // 优化构建配置
  if (!config.build) {
    config.build = {}
  }
  
  // 启用优化
  config.build.withGlobalTauri = false
  config.build.beforeDevCommand = ""
  config.build.beforeBuildCommand = "npm run build"
  config.build.devUrl = "http://localhost:1420"
  config.build.frontendDist = "../dist"
  
  // 优化窗口配置
  if (config.app && config.app.windows && config.app.windows[0]) {
    const window = config.app.windows[0]
    
    // 启动优化
    window.visible = false // 启动时隐藏，加载完成后显示
    window.center = true
    
    // 性能优化
    if (!window.webSecurity) {
      window.webSecurity = true
    }
  }
  
  // 优化安全配置
  if (!config.app.security) {
    config.app.security = {}
  }
  config.app.security.csp = "default-src 'self'; style-src 'self' 'unsafe-inline'"
  
  fs.writeFileSync(tauriConfigPath, JSON.stringify(config, null, 2))
  console.log('✅ Tauri 配置已优化')
  
  return true
}

/**
 * 优化 package.json 脚本
 */
function optimizePackageScripts() {
  const packagePath = path.join(__dirname, '../package.json')
  
  if (!fs.existsSync(packagePath)) {
    console.error('❌ package.json 文件不存在')
    return false
  }
  
  const pkg = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
  
  // 添加优化脚本
  if (!pkg.scripts['build:analyze']) {
    pkg.scripts['build:analyze'] = 'vite build --mode analyze'
  }
  
  if (!pkg.scripts['build:prod']) {
    pkg.scripts['build:prod'] = 'vite build --mode production'
  }
  
  if (!pkg.scripts['tauri:build:release']) {
    pkg.scripts['tauri:build:release'] = 'tauri build --release'
  }
  
  fs.writeFileSync(packagePath, JSON.stringify(pkg, null, 2))
  console.log('✅ Package.json 脚本已优化')
  
  return true
}

/**
 * 创建性能监控脚本
 */
function createPerformanceMonitor() {
  const monitorScript = `
/**
 * 性能监控工具
 */

class PerformanceMonitor {
  constructor() {
    this.metrics = {
      loadTime: 0,
      renderTime: 0,
      memoryUsage: 0,
      bundleSize: 0
    }
    
    this.init()
  }
  
  init() {
    // 监控页面加载时间
    window.addEventListener('load', () => {
      this.metrics.loadTime = performance.now()
      console.log('📊 页面加载时间:', this.metrics.loadTime.toFixed(2), 'ms')
    })
    
    // 监控内存使用
    if (performance.memory) {
      setInterval(() => {
        this.metrics.memoryUsage = performance.memory.usedJSHeapSize
        
        // 内存使用超过 100MB 时警告
        if (this.metrics.memoryUsage > 100 * 1024 * 1024) {
          console.warn('⚠️ 内存使用过高:', (this.metrics.memoryUsage / 1024 / 1024).toFixed(2), 'MB')
        }
      }, 5000)
    }
    
    // 监控长任务
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.duration > 50) {
            console.warn('⚠️ 检测到长任务:', entry.duration.toFixed(2), 'ms')
          }
        }
      })
      
      observer.observe({ entryTypes: ['longtask'] })
    }
  }
  
  // 获取性能报告
  getReport() {
    return {
      ...this.metrics,
      timestamp: Date.now(),
      userAgent: navigator.userAgent
    }
  }
  
  // 导出性能数据
  exportData() {
    const data = this.getReport()
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    
    const a = document.createElement('a')
    a.href = url
    a.download = \`performance-report-\${Date.now()}.json\`
    a.click()
    
    URL.revokeObjectURL(url)
  }
}

// 在开发环境中启用性能监控
if (import.meta.env.DEV) {
  window.performanceMonitor = new PerformanceMonitor()
  
  // 添加全局快捷键 Ctrl+Shift+P 导出性能数据
  document.addEventListener('keydown', (e) => {
    if (e.ctrlKey && e.shiftKey && e.key === 'P') {
      window.performanceMonitor.exportData()
    }
  })
}

export default PerformanceMonitor
`
  
  const monitorPath = path.join(__dirname, '../src/utils/performance.ts')
  fs.writeFileSync(monitorPath, monitorScript)
  console.log('✅ 性能监控脚本已创建')
}

/**
 * 主优化函数
 */
function optimize() {
  console.log('🚀 开始应用性能优化...\n')
  
  const results = []
  
  // 执行各项优化
  results.push(optimizeViteConfig())
  results.push(optimizeTauriConfig())
  results.push(optimizePackageScripts())
  
  // 创建性能监控
  createPerformanceMonitor()
  
  // 输出结果
  const successCount = results.filter(Boolean).length
  const totalCount = results.length
  
  console.log(`\n✨ 优化完成！成功 ${successCount}/${totalCount} 项`)
  
  if (successCount === totalCount) {
    console.log('\n📋 优化建议：')
    console.log('1. 运行 npm run build:analyze 分析包体积')
    console.log('2. 运行 npm run test:performance 检查性能')
    console.log('3. 使用 Ctrl+Shift+P 导出性能数据（开发环境）')
    console.log('4. 定期检查内存使用情况')
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  optimize()
}

export {
  optimize,
  optimizeViteConfig,
  optimizeTauriConfig,
  optimizePackageScripts,
  createPerformanceMonitor
}
