D:\projects\vscode-projects\erweima\MyToolkit\src-tauri\target\debug\deps\app_lib-4e6f20e368205d6b.exe: src\lib.rs src\tests.rs D:\projects\vscode-projects\erweima\MyToolkit\src-tauri\target\debug\build\app-3df8e1f29ff16b36\out/45f1706e214e051e2ea25d5fcefb1d3a2ca8c5cc7ea0df00a57ce212cf0da8c5

D:\projects\vscode-projects\erweima\MyToolkit\src-tauri\target\debug\deps\app_lib-4e6f20e368205d6b.d: src\lib.rs src\tests.rs D:\projects\vscode-projects\erweima\MyToolkit\src-tauri\target\debug\build\app-3df8e1f29ff16b36\out/45f1706e214e051e2ea25d5fcefb1d3a2ca8c5cc7ea0df00a57ce212cf0da8c5

src\lib.rs:
src\tests.rs:
D:\projects\vscode-projects\erweima\MyToolkit\src-tauri\target\debug\build\app-3df8e1f29ff16b36\out/45f1706e214e051e2ea25d5fcefb1d3a2ca8c5cc7ea0df00a57ce212cf0da8c5:

# env-dep:CARGO_PKG_AUTHORS=you
# env-dep:CARGO_PKG_DESCRIPTION=A Tauri App
# env-dep:CARGO_PKG_NAME=app
# env-dep:CARGO_PKG_VERSION=0.1.0
# env-dep:OUT_DIR=D:\\projects\\vscode-projects\\erweima\\MyToolkit\\src-tauri\\target\\debug\\build\\app-3df8e1f29ff16b36\\out
